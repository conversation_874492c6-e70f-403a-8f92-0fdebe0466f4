/**
 * Users API endpoint
 * Handles user management operations (list, create)
 */

import { NextRequest } from 'next/server';
import { createResponse, createErrorResponse, parsePaginationParams, parseFilterParams } from '@/lib/utils';
import { getUserFromRequest, hasPermission } from '@/lib/auth';
import { query } from '@/lib/db';
import { UserRole } from '@/types';

interface User {
  id: string;
  email: string;
  role: UserRole;
  name: string;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}

// GET /api/users - List users with pagination and filtering
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    // Check permissions
    if (!hasPermission(authResult.user.role, 'users', 'read')) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const { searchParams } = new URL(request.url);
    const pagination = parsePaginationParams(searchParams);
    const filters = parseFilterParams(searchParams);

    try {
      // Build query with filters
      let whereClause = 'WHERE 1=1';
      const queryParams: any[] = [];
      let paramIndex = 1;

      if (filters.search) {
        whereClause += ` AND (name ILIKE $${paramIndex} OR email ILIKE $${paramIndex})`;
        queryParams.push(`%${filters.search}%`);
        paramIndex++;
      }

      if (filters.role) {
        whereClause += ` AND role = $${paramIndex}`;
        queryParams.push(filters.role);
        paramIndex++;
      }

      if (filters.isActive !== undefined) {
        whereClause += ` AND is_active = $${paramIndex}`;
        queryParams.push(filters.isActive === 'true');
        paramIndex++;
      }

      // Get total count
      const countResult = await query(
        `SELECT COUNT(*) as total FROM users ${whereClause}`,
        queryParams
      );
      const total = parseInt(countResult.rows[0].total);

      // Get users with pagination
      const offset = (pagination.page - 1) * pagination.limit;
      const usersResult = await query<User>(
        `SELECT id, email, role, name, is_active, created_at, updated_at 
         FROM users ${whereClause} 
         ORDER BY created_at DESC 
         LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`,
        [...queryParams, pagination.limit, offset]
      );

      return createResponse({
        users: usersResult.rows,
        pagination: {
          page: pagination.page,
          limit: pagination.limit,
          total,
          totalPages: Math.ceil(total / pagination.limit),
          hasNext: pagination.page < Math.ceil(total / pagination.limit),
          hasPrev: pagination.page > 1
        }
      }, true, 'Users retrieved successfully');

    } catch (dbError) {
      console.error('Database error getting users:', dbError);
      
      // For development, return mock data if database is not available
      if (process.env.NODE_ENV === 'development') {
        const mockUsers = [
          {
            id: 'mock-admin-id',
            email: '<EMAIL>',
            role: UserRole.ADMIN,
            name: 'System Administrator',
            is_active: true,
            created_at: new Date(),
            updated_at: new Date()
          },
          {
            id: 'mock-cashier-id',
            email: '<EMAIL>',
            role: UserRole.CASHIER,
            name: 'Cashier User',
            is_active: true,
            created_at: new Date(),
            updated_at: new Date()
          }
        ];

        return createResponse({
          users: mockUsers,
          pagination: {
            page: 1,
            limit: 20,
            total: mockUsers.length,
            totalPages: 1,
            hasNext: false,
            hasPrev: false
          }
        }, true, 'Users retrieved successfully (development mode)');
      }
      
      return createErrorResponse('User service temporarily unavailable', 503);
    }

  } catch (error) {
    console.error('Get users error:', error);
    return createErrorResponse('Failed to get users', 500);
  }
}

// POST /api/users - Create new user
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    // Check permissions
    if (!hasPermission(authResult.user.role, 'users', 'create')) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    // For now, return a placeholder response
    return createErrorResponse('User creation not yet implemented', 501);

  } catch (error) {
    console.error('Create user error:', error);
    return createErrorResponse('Failed to create user', 500);
  }
}

// Handle OPTIONS request for CORS
export async function OPTIONS(request: NextRequest) {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
