{"name": "innovative-centre-admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3000", "build": "next build", "start": "next start -p 3000", "lint": "next lint", "db:migrate": "node scripts/migrate.js"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/pg": "^8.15.4", "bcryptjs": "^3.0.2", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "jsonwebtoken": "^9.0.2", "next": "15.3.4", "pg": "^8.16.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.58.1", "recharts": "^3.0.0", "tailwind-merge": "^3.3.1", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "typescript": "^5"}}