/**
 * Database migration script for Admin Server
 * Runs the admin database schema and initial data setup
 */

const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

// Database configuration
const dbConfig = {
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
};

async function runMigration() {
  const pool = new Pool(dbConfig);
  
  try {
    console.log('🚀 Starting database migration...');
    
    // Test connection
    console.log('📡 Testing database connection...');
    const testResult = await pool.query('SELECT NOW() as current_time');
    console.log('✅ Database connected successfully:', testResult.rows[0].current_time);
    
    // Read and execute admin schema
    console.log('📋 Reading admin schema...');
    const schemaPath = path.join(__dirname, '../../shared/database/admin-schema.sql');
    
    if (!fs.existsSync(schemaPath)) {
      throw new Error(`Schema file not found: ${schemaPath}`);
    }
    
    const schemaSql = fs.readFileSync(schemaPath, 'utf8');
    
    console.log('🔧 Executing admin schema...');
    await pool.query(schemaSql);
    console.log('✅ Admin schema executed successfully');
    
    // Verify tables were created
    console.log('🔍 Verifying table creation...');
    const tablesResult = await pool.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `);
    
    const tables = tablesResult.rows.map(row => row.table_name);
    console.log('📊 Created tables:', tables);
    
    // Check if default admin user exists
    console.log('👤 Checking for default admin user...');
    const userResult = await pool.query(
      'SELECT id, email, role FROM users WHERE email = $1',
      ['<EMAIL>']
    );
    
    if (userResult.rows.length > 0) {
      console.log('✅ Default admin user found:', userResult.rows[0]);
    } else {
      console.log('⚠️  Default admin user not found');
    }
    
    // Display migration summary
    console.log('\n📈 Migration Summary:');
    console.log('='.repeat(50));
    
    for (const table of tables) {
      const countResult = await pool.query(`SELECT COUNT(*) as count FROM ${table}`);
      console.log(`📋 ${table}: ${countResult.rows[0].count} records`);
    }
    
    console.log('='.repeat(50));
    console.log('🎉 Database migration completed successfully!');
    console.log('\n📝 Next steps:');
    console.log('1. Start the admin server: npm run dev');
    console.log('2. Login with: <EMAIL> / Admin123!');
    console.log('3. Change the default password immediately');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Handle command line arguments
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
  console.log(`
📚 Database Migration Script

Usage: node scripts/migrate.js [options]

Options:
  --help, -h     Show this help message
  --force        Force migration (drops existing tables)
  --dry-run      Show what would be executed without running

Environment Variables:
  DATABASE_URL   PostgreSQL connection string (required)

Examples:
  node scripts/migrate.js
  node scripts/migrate.js --force
  node scripts/migrate.js --dry-run
  `);
  process.exit(0);
}

if (args.includes('--dry-run')) {
  console.log('🔍 Dry run mode - showing what would be executed:');
  console.log('1. Connect to database');
  console.log('2. Execute admin-schema.sql');
  console.log('3. Verify table creation');
  console.log('4. Check default admin user');
  console.log('5. Display summary');
  process.exit(0);
}

if (args.includes('--force')) {
  console.log('⚠️  Force mode enabled - this will drop existing tables!');
  console.log('Press Ctrl+C to cancel or wait 5 seconds to continue...');
  
  setTimeout(() => {
    runMigration();
  }, 5000);
} else {
  // Check if DATABASE_URL is provided
  if (!process.env.DATABASE_URL) {
    console.error('❌ DATABASE_URL environment variable is required');
    console.log('💡 Make sure you have .env.local file with DATABASE_URL');
    process.exit(1);
  }
  
  runMigration();
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n⏹️  Migration cancelled by user');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n⏹️  Migration terminated');
  process.exit(0);
});
